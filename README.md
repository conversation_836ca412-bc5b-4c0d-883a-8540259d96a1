# 智能路亚钓鱼建议系统

基于DeepSeek大模型的专业级垂钓方案定制系统，使用Next.js构建，集成用户认证和历史记录功能。

## 功能特点

### 核心功能
- 基于环境参数生成专业钓鱼建议
- 详细的分析和建议，包括装备优化、拟饵策略等
- 响应式设计，适配各种设备
- 支持多种鱼种和环境条件

### 用户系统
- 用户注册和登录（邮箱/密码）
- 第三方登录（Google、GitHub）
- 个人资料管理
- 安全的会话管理

### 历史记录
- 自动保存钓鱼建议历史
- 收藏功能
- 历史记录查看和管理
- 数据导出功能

### 安全特性
- 基于Supabase的认证系统
- 行级安全策略（RLS）
- 受保护的路由
- 数据隐私保护

## 本地部署指南

### 前提条件

- Node.js 18.0.0 或更高版本
- npm 或 yarn 包管理器
- DeepSeek API 密钥
- Supabase 项目（用于认证和数据库）

### 安装步骤

#### 1. 克隆仓库

```bash
git clone https://github.com/yourusername/fishing-advisor.git
cd fishing-advisor
```

#### 2. 安装依赖

```bash
npm install
```

#### 3. 设置Supabase

1. 在 [Supabase](https://supabase.com) 创建新项目
2. 在项目的SQL编辑器中运行 `supabase-setup.sql` 文件中的SQL语句
3. 在认证设置中启用邮箱登录和第三方登录（可选）
4. 获取项目URL和匿名密钥

#### 4. 配置环境变量

复制 `.env.example` 文件为 `.env.local`：

```bash
cp .env.example .env.local
```

编辑 `.env.local` 文件，填入您的API密钥和Supabase配置：

```env
# DeepSeek API Key
DEEPSEEK_API_KEY=your_deepseek_api_key_here

# Supabase Configuration
NEXT_PUBLIC_SUPABASE_URL=your_supabase_project_url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key
```

#### 5. 运行开发服务器

```bash
npm run dev
```

打开浏览器访问 [http://localhost:3000](http://localhost:3000) 查看应用。

## 使用说明

### 基本使用

1. **注册账户**：首次使用需要注册账户
2. **填写参数**：在主页填写钓鱼环境参数
3. **生成建议**：点击"生成钓鱼建议"获取专业建议
4. **保存记录**：登录用户的建议会自动保存到历史记录

### 功能说明

- **环境分析**：基于您提供的参数进行环境分析
- **装备优化**：推荐最适合的钓具配置
- **拟饵策略**：建议最有效的拟饵选择和使用方法
- **战术要点**：提供具体的钓鱼技巧和注意事项
- **时段计划**：制定最佳的垂钓时间安排
- **进阶技巧**：分享高级钓鱼技巧
- **应急预案**：应对突发情况的备用方案

## 技术栈

- **前端**：Next.js 14, React 18, TypeScript
- **样式**：Tailwind CSS, Radix UI
- **认证**：Supabase Auth
- **数据库**：Supabase (PostgreSQL)
- **表单**：React Hook Form + Zod
- **AI服务**：DeepSeek API

## 项目结构

```
├── app/                    # Next.js App Router
│   ├── login/             # 登录页面
│   ├── register/          # 注册页面
│   ├── profile/           # 个人资料
│   ├── history/           # 历史记录
│   ├── settings/          # 设置页面
│   └── auth/callback/     # 认证回调
├── components/            # React组件
│   ├── ui/               # 基础UI组件
│   ├── navbar.tsx        # 导航栏
│   └── fishing-advisor.tsx # 主要功能组件
├── lib/                  # 工具库
│   ├── supabase.ts       # Supabase配置
│   ├── auth-context.tsx  # 认证上下文
│   ├── fishing-history.ts # 历史记录功能
│   └── actions.ts        # 服务器操作
└── middleware.ts         # 路由中间件
```

## 部署

### Vercel部署

1. 将代码推送到GitHub
2. 在Vercel中导入项目
3. 配置环境变量
4. 部署

### 环境变量配置

确保在生产环境中设置以下环境变量：

```env
DEEPSEEK_API_KEY=your_deepseek_api_key
NEXT_PUBLIC_SUPABASE_URL=your_supabase_url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key
```

## 贡献

欢迎提交Issue和Pull Request来改进这个项目。

## 许可证

MIT License

