"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/react-hook-form";
exports.ids = ["vendor-chunks/react-hook-form"];
exports.modules = {

/***/ "(ssr)/./node_modules/react-hook-form/dist/index.esm.mjs":
/*!*********************************************************!*\
  !*** ./node_modules/react-hook-form/dist/index.esm.mjs ***!
  \*********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Controller: () => (/* binding */ Controller),\n/* harmony export */   Form: () => (/* binding */ Form),\n/* harmony export */   FormProvider: () => (/* binding */ FormProvider),\n/* harmony export */   appendErrors: () => (/* binding */ appendErrors),\n/* harmony export */   get: () => (/* binding */ get),\n/* harmony export */   set: () => (/* binding */ set),\n/* harmony export */   useController: () => (/* binding */ useController),\n/* harmony export */   useFieldArray: () => (/* binding */ useFieldArray),\n/* harmony export */   useForm: () => (/* binding */ useForm),\n/* harmony export */   useFormContext: () => (/* binding */ useFormContext),\n/* harmony export */   useFormState: () => (/* binding */ useFormState),\n/* harmony export */   useWatch: () => (/* binding */ useWatch)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n\nvar isCheckBoxInput = (element)=>element.type === \"checkbox\";\nvar isDateObject = (value1)=>value1 instanceof Date;\nvar isNullOrUndefined = (value1)=>value1 == null;\nconst isObjectType = (value1)=>typeof value1 === \"object\";\nvar isObject = (value1)=>!isNullOrUndefined(value1) && !Array.isArray(value1) && isObjectType(value1) && !isDateObject(value1);\nvar getEventValue = (event)=>isObject(event) && event.target ? isCheckBoxInput(event.target) ? event.target.checked : event.target.value : event;\nvar getNodeParentName = (name)=>name.substring(0, name.search(/\\.\\d+(\\.|$)/)) || name;\nvar isNameInFieldArray = (names, name)=>names.has(getNodeParentName(name));\nvar isPlainObject = (tempObject)=>{\n    const prototypeCopy = tempObject.constructor && tempObject.constructor.prototype;\n    return isObject(prototypeCopy) && prototypeCopy.hasOwnProperty(\"isPrototypeOf\");\n};\nvar isWeb =  false && 0;\nfunction cloneObject(data) {\n    let copy;\n    const isArray = Array.isArray(data);\n    const isFileListInstance = typeof FileList !== \"undefined\" ? data instanceof FileList : false;\n    if (data instanceof Date) {\n        copy = new Date(data);\n    } else if (data instanceof Set) {\n        copy = new Set(data);\n    } else if (!(isWeb && (data instanceof Blob || isFileListInstance)) && (isArray || isObject(data))) {\n        copy = isArray ? [] : {};\n        if (!isArray && !isPlainObject(data)) {\n            copy = data;\n        } else {\n            for(const key in data){\n                if (data.hasOwnProperty(key)) {\n                    copy[key] = cloneObject(data[key]);\n                }\n            }\n        }\n    } else {\n        return data;\n    }\n    return copy;\n}\nvar compact = (value1)=>Array.isArray(value1) ? value1.filter(Boolean) : [];\nvar isUndefined = (val)=>val === undefined;\nvar get = (object, path, defaultValue)=>{\n    if (!path || !isObject(object)) {\n        return defaultValue;\n    }\n    const result = compact(path.split(/[,[\\].]+?/)).reduce((result, key)=>isNullOrUndefined(result) ? result : result[key], object);\n    return isUndefined(result) || result === object ? isUndefined(object[path]) ? defaultValue : object[path] : result;\n};\nvar isBoolean = (value1)=>typeof value1 === \"boolean\";\nvar isKey = (value1)=>/^\\w*$/.test(value1);\nvar stringToPath = (input)=>compact(input.replace(/[\"|']|\\]/g, \"\").split(/\\.|\\[/));\nvar set = (object, path, value1)=>{\n    let index = -1;\n    const tempPath = isKey(path) ? [\n        path\n    ] : stringToPath(path);\n    const length = tempPath.length;\n    const lastIndex = length - 1;\n    while(++index < length){\n        const key = tempPath[index];\n        let newValue = value1;\n        if (index !== lastIndex) {\n            const objValue = object[key];\n            newValue = isObject(objValue) || Array.isArray(objValue) ? objValue : !isNaN(+tempPath[index + 1]) ? [] : {};\n        }\n        if (key === \"__proto__\" || key === \"constructor\" || key === \"prototype\") {\n            return;\n        }\n        object[key] = newValue;\n        object = object[key];\n    }\n    return object;\n};\nconst EVENTS = {\n    BLUR: \"blur\",\n    FOCUS_OUT: \"focusout\",\n    CHANGE: \"change\"\n};\nconst VALIDATION_MODE = {\n    onBlur: \"onBlur\",\n    onChange: \"onChange\",\n    onSubmit: \"onSubmit\",\n    onTouched: \"onTouched\",\n    all: \"all\"\n};\nconst INPUT_VALIDATION_RULES = {\n    max: \"max\",\n    min: \"min\",\n    maxLength: \"maxLength\",\n    minLength: \"minLength\",\n    pattern: \"pattern\",\n    required: \"required\",\n    validate: \"validate\"\n};\nconst HookFormContext = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createContext(null);\n/**\n * This custom hook allows you to access the form context. useFormContext is intended to be used in deeply nested structures, where it would become inconvenient to pass the context as a prop. To be used with {@link FormProvider}.\n *\n * @remarks\n * [API](https://react-hook-form.com/docs/useformcontext) • [Demo](https://codesandbox.io/s/react-hook-form-v7-form-context-ytudi)\n *\n * @returns return all useForm methods\n *\n * @example\n * ```tsx\n * function App() {\n *   const methods = useForm();\n *   const onSubmit = data => console.log(data);\n *\n *   return (\n *     <FormProvider {...methods} >\n *       <form onSubmit={methods.handleSubmit(onSubmit)}>\n *         <NestedInput />\n *         <input type=\"submit\" />\n *       </form>\n *     </FormProvider>\n *   );\n * }\n *\n *  function NestedInput() {\n *   const { register } = useFormContext(); // retrieve all hook methods\n *   return <input {...register(\"test\")} />;\n * }\n * ```\n */ const useFormContext = ()=>react__WEBPACK_IMPORTED_MODULE_0__.useContext(HookFormContext);\n/**\n * A provider component that propagates the `useForm` methods to all children components via [React Context](https://reactjs.org/docs/context.html) API. To be used with {@link useFormContext}.\n *\n * @remarks\n * [API](https://react-hook-form.com/docs/useformcontext) • [Demo](https://codesandbox.io/s/react-hook-form-v7-form-context-ytudi)\n *\n * @param props - all useForm methods\n *\n * @example\n * ```tsx\n * function App() {\n *   const methods = useForm();\n *   const onSubmit = data => console.log(data);\n *\n *   return (\n *     <FormProvider {...methods} >\n *       <form onSubmit={methods.handleSubmit(onSubmit)}>\n *         <NestedInput />\n *         <input type=\"submit\" />\n *       </form>\n *     </FormProvider>\n *   );\n * }\n *\n *  function NestedInput() {\n *   const { register } = useFormContext(); // retrieve all hook methods\n *   return <input {...register(\"test\")} />;\n * }\n * ```\n */ const FormProvider = (props)=>{\n    const { children, ...data } = props;\n    return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(HookFormContext.Provider, {\n        value: data\n    }, children);\n};\nvar getProxyFormState = (formState, control, localProxyFormState, isRoot = true)=>{\n    const result = {\n        defaultValues: control._defaultValues\n    };\n    for(const key in formState){\n        Object.defineProperty(result, key, {\n            get: ()=>{\n                const _key = key;\n                if (control._proxyFormState[_key] !== VALIDATION_MODE.all) {\n                    control._proxyFormState[_key] = !isRoot || VALIDATION_MODE.all;\n                }\n                localProxyFormState && (localProxyFormState[_key] = true);\n                return formState[_key];\n            }\n        });\n    }\n    return result;\n};\nvar isEmptyObject = (value1)=>isObject(value1) && !Object.keys(value1).length;\nvar shouldRenderFormState = (formStateData, _proxyFormState, updateFormState, isRoot)=>{\n    updateFormState(formStateData);\n    const { name, ...formState } = formStateData;\n    return isEmptyObject(formState) || Object.keys(formState).length >= Object.keys(_proxyFormState).length || Object.keys(formState).find((key)=>_proxyFormState[key] === (!isRoot || VALIDATION_MODE.all));\n};\nvar convertToArrayPayload = (value1)=>Array.isArray(value1) ? value1 : [\n        value1\n    ];\nvar shouldSubscribeByName = (name, signalName, exact)=>!name || !signalName || name === signalName || convertToArrayPayload(name).some((currentName)=>currentName && (exact ? currentName === signalName : currentName.startsWith(signalName) || signalName.startsWith(currentName)));\nfunction useSubscribe(props) {\n    const _props = react__WEBPACK_IMPORTED_MODULE_0__.useRef(props);\n    _props.current = props;\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        const subscription = !props.disabled && _props.current.subject && _props.current.subject.subscribe({\n            next: _props.current.next\n        });\n        return ()=>{\n            subscription && subscription.unsubscribe();\n        };\n    }, [\n        props.disabled\n    ]);\n}\n/**\n * This custom hook allows you to subscribe to each form state, and isolate the re-render at the custom hook level. It has its scope in terms of form state subscription, so it would not affect other useFormState and useForm. Using this hook can reduce the re-render impact on large and complex form application.\n *\n * @remarks\n * [API](https://react-hook-form.com/docs/useformstate) • [Demo](https://codesandbox.io/s/useformstate-75xly)\n *\n * @param props - include options on specify fields to subscribe. {@link UseFormStateReturn}\n *\n * @example\n * ```tsx\n * function App() {\n *   const { register, handleSubmit, control } = useForm({\n *     defaultValues: {\n *     firstName: \"firstName\"\n *   }});\n *   const { dirtyFields } = useFormState({\n *     control\n *   });\n *   const onSubmit = (data) => console.log(data);\n *\n *   return (\n *     <form onSubmit={handleSubmit(onSubmit)}>\n *       <input {...register(\"firstName\")} placeholder=\"First Name\" />\n *       {dirtyFields.firstName && <p>Field is dirty.</p>}\n *       <input type=\"submit\" />\n *     </form>\n *   );\n * }\n * ```\n */ function useFormState(props) {\n    const methods = useFormContext();\n    const { control = methods.control, disabled, name, exact } = props || {};\n    const [formState, updateFormState] = react__WEBPACK_IMPORTED_MODULE_0__.useState(control._formState);\n    const _mounted = react__WEBPACK_IMPORTED_MODULE_0__.useRef(true);\n    const _localProxyFormState = react__WEBPACK_IMPORTED_MODULE_0__.useRef({\n        isDirty: false,\n        isLoading: false,\n        dirtyFields: false,\n        touchedFields: false,\n        validatingFields: false,\n        isValidating: false,\n        isValid: false,\n        errors: false\n    });\n    const _name = react__WEBPACK_IMPORTED_MODULE_0__.useRef(name);\n    _name.current = name;\n    useSubscribe({\n        disabled,\n        next: (value1)=>_mounted.current && shouldSubscribeByName(_name.current, value1.name, exact) && shouldRenderFormState(value1, _localProxyFormState.current, control._updateFormState) && updateFormState({\n                ...control._formState,\n                ...value1\n            }),\n        subject: control._subjects.state\n    });\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        _mounted.current = true;\n        _localProxyFormState.current.isValid && control._updateValid(true);\n        return ()=>{\n            _mounted.current = false;\n        };\n    }, [\n        control\n    ]);\n    return react__WEBPACK_IMPORTED_MODULE_0__.useMemo(()=>getProxyFormState(formState, control, _localProxyFormState.current, false), [\n        formState,\n        control\n    ]);\n}\nvar isString = (value1)=>typeof value1 === \"string\";\nvar generateWatchOutput = (names, _names, formValues, isGlobal, defaultValue)=>{\n    if (isString(names)) {\n        isGlobal && _names.watch.add(names);\n        return get(formValues, names, defaultValue);\n    }\n    if (Array.isArray(names)) {\n        return names.map((fieldName)=>(isGlobal && _names.watch.add(fieldName), get(formValues, fieldName)));\n    }\n    isGlobal && (_names.watchAll = true);\n    return formValues;\n};\n/**\n * Custom hook to subscribe to field change and isolate re-rendering at the component level.\n *\n * @remarks\n *\n * [API](https://react-hook-form.com/docs/usewatch) • [Demo](https://codesandbox.io/s/react-hook-form-v7-ts-usewatch-h9i5e)\n *\n * @example\n * ```tsx\n * const { control } = useForm();\n * const values = useWatch({\n *   name: \"fieldName\"\n *   control,\n * })\n * ```\n */ function useWatch(props) {\n    const methods = useFormContext();\n    const { control = methods.control, name, defaultValue, disabled, exact } = props || {};\n    const _name = react__WEBPACK_IMPORTED_MODULE_0__.useRef(name);\n    _name.current = name;\n    useSubscribe({\n        disabled,\n        subject: control._subjects.values,\n        next: (formState)=>{\n            if (shouldSubscribeByName(_name.current, formState.name, exact)) {\n                updateValue(cloneObject(generateWatchOutput(_name.current, control._names, formState.values || control._formValues, false, defaultValue)));\n            }\n        }\n    });\n    const [value1, updateValue] = react__WEBPACK_IMPORTED_MODULE_0__.useState(control._getWatch(name, defaultValue));\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>control._removeUnmounted());\n    return value1;\n}\n/**\n * Custom hook to work with controlled component, this function provide you with both form and field level state. Re-render is isolated at the hook level.\n *\n * @remarks\n * [API](https://react-hook-form.com/docs/usecontroller) • [Demo](https://codesandbox.io/s/usecontroller-0o8px)\n *\n * @param props - the path name to the form field value, and validation rules.\n *\n * @returns field properties, field and form state. {@link UseControllerReturn}\n *\n * @example\n * ```tsx\n * function Input(props) {\n *   const { field, fieldState, formState } = useController(props);\n *   return (\n *     <div>\n *       <input {...field} placeholder={props.name} />\n *       <p>{fieldState.isTouched && \"Touched\"}</p>\n *       <p>{formState.isSubmitted ? \"submitted\" : \"\"}</p>\n *     </div>\n *   );\n * }\n * ```\n */ function useController(props) {\n    const methods = useFormContext();\n    const { name, disabled, control = methods.control, shouldUnregister } = props;\n    const isArrayField = isNameInFieldArray(control._names.array, name);\n    const value1 = useWatch({\n        control,\n        name,\n        defaultValue: get(control._formValues, name, get(control._defaultValues, name, props.defaultValue)),\n        exact: true\n    });\n    const formState = useFormState({\n        control,\n        name,\n        exact: true\n    });\n    const _registerProps = react__WEBPACK_IMPORTED_MODULE_0__.useRef(control.register(name, {\n        ...props.rules,\n        value: value1,\n        ...isBoolean(props.disabled) ? {\n            disabled: props.disabled\n        } : {}\n    }));\n    const fieldState = react__WEBPACK_IMPORTED_MODULE_0__.useMemo(()=>Object.defineProperties({}, {\n            invalid: {\n                enumerable: true,\n                get: ()=>!!get(formState.errors, name)\n            },\n            isDirty: {\n                enumerable: true,\n                get: ()=>!!get(formState.dirtyFields, name)\n            },\n            isTouched: {\n                enumerable: true,\n                get: ()=>!!get(formState.touchedFields, name)\n            },\n            isValidating: {\n                enumerable: true,\n                get: ()=>!!get(formState.validatingFields, name)\n            },\n            error: {\n                enumerable: true,\n                get: ()=>get(formState.errors, name)\n            }\n        }), [\n        formState,\n        name\n    ]);\n    const field = react__WEBPACK_IMPORTED_MODULE_0__.useMemo(()=>({\n            name,\n            value: value1,\n            ...isBoolean(disabled) || formState.disabled ? {\n                disabled: formState.disabled || disabled\n            } : {},\n            onChange: (event)=>_registerProps.current.onChange({\n                    target: {\n                        value: getEventValue(event),\n                        name: name\n                    },\n                    type: EVENTS.CHANGE\n                }),\n            onBlur: ()=>_registerProps.current.onBlur({\n                    target: {\n                        value: get(control._formValues, name),\n                        name: name\n                    },\n                    type: EVENTS.BLUR\n                }),\n            ref: (elm)=>{\n                const field = get(control._fields, name);\n                if (field && elm) {\n                    field._f.ref = {\n                        focus: ()=>elm.focus(),\n                        select: ()=>elm.select(),\n                        setCustomValidity: (message)=>elm.setCustomValidity(message),\n                        reportValidity: ()=>elm.reportValidity()\n                    };\n                }\n            }\n        }), [\n        name,\n        control._formValues,\n        disabled,\n        formState.disabled,\n        value1,\n        control._fields\n    ]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        const _shouldUnregisterField = control._options.shouldUnregister || shouldUnregister;\n        const updateMounted = (name, value1)=>{\n            const field = get(control._fields, name);\n            if (field && field._f) {\n                field._f.mount = value1;\n            }\n        };\n        updateMounted(name, true);\n        if (_shouldUnregisterField) {\n            const value1 = cloneObject(get(control._options.defaultValues, name));\n            set(control._defaultValues, name, value1);\n            if (isUndefined(get(control._formValues, name))) {\n                set(control._formValues, name, value1);\n            }\n        }\n        !isArrayField && control.register(name);\n        return ()=>{\n            (isArrayField ? _shouldUnregisterField && !control._state.action : _shouldUnregisterField) ? control.unregister(name) : updateMounted(name, false);\n        };\n    }, [\n        name,\n        control,\n        isArrayField,\n        shouldUnregister\n    ]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        control._updateDisabledField({\n            disabled,\n            fields: control._fields,\n            name\n        });\n    }, [\n        disabled,\n        name,\n        control\n    ]);\n    return react__WEBPACK_IMPORTED_MODULE_0__.useMemo(()=>({\n            field,\n            formState,\n            fieldState\n        }), [\n        field,\n        formState,\n        fieldState\n    ]);\n}\n/**\n * Component based on `useController` hook to work with controlled component.\n *\n * @remarks\n * [API](https://react-hook-form.com/docs/usecontroller/controller) • [Demo](https://codesandbox.io/s/react-hook-form-v6-controller-ts-jwyzw) • [Video](https://www.youtube.com/watch?v=N2UNk_UCVyA)\n *\n * @param props - the path name to the form field value, and validation rules.\n *\n * @returns provide field handler functions, field and form state.\n *\n * @example\n * ```tsx\n * function App() {\n *   const { control } = useForm<FormValues>({\n *     defaultValues: {\n *       test: \"\"\n *     }\n *   });\n *\n *   return (\n *     <form>\n *       <Controller\n *         control={control}\n *         name=\"test\"\n *         render={({ field: { onChange, onBlur, value, ref }, formState, fieldState }) => (\n *           <>\n *             <input\n *               onChange={onChange} // send value to hook form\n *               onBlur={onBlur} // notify when input is touched\n *               value={value} // return updated value\n *               ref={ref} // set ref for focus management\n *             />\n *             <p>{formState.isSubmitted ? \"submitted\" : \"\"}</p>\n *             <p>{fieldState.isTouched ? \"touched\" : \"\"}</p>\n *           </>\n *         )}\n *       />\n *     </form>\n *   );\n * }\n * ```\n */ const Controller = (props)=>props.render(useController(props));\nconst flatten = (obj)=>{\n    const output = {};\n    for (const key of Object.keys(obj)){\n        if (isObjectType(obj[key]) && obj[key] !== null) {\n            const nested = flatten(obj[key]);\n            for (const nestedKey of Object.keys(nested)){\n                output[`${key}.${nestedKey}`] = nested[nestedKey];\n            }\n        } else {\n            output[key] = obj[key];\n        }\n    }\n    return output;\n};\nconst POST_REQUEST = \"post\";\n/**\n * Form component to manage submission.\n *\n * @param props - to setup submission detail. {@link FormProps}\n *\n * @returns form component or headless render prop.\n *\n * @example\n * ```tsx\n * function App() {\n *   const { control, formState: { errors } } = useForm();\n *\n *   return (\n *     <Form action=\"/api\" control={control}>\n *       <input {...register(\"name\")} />\n *       <p>{errors?.root?.server && 'Server error'}</p>\n *       <button>Submit</button>\n *     </Form>\n *   );\n * }\n * ```\n */ function Form(props) {\n    const methods = useFormContext();\n    const [mounted, setMounted] = react__WEBPACK_IMPORTED_MODULE_0__.useState(false);\n    const { control = methods.control, onSubmit, children, action, method = POST_REQUEST, headers, encType, onError, render, onSuccess, validateStatus, ...rest } = props;\n    const submit = async (event)=>{\n        let hasError = false;\n        let type = \"\";\n        await control.handleSubmit(async (data)=>{\n            const formData = new FormData();\n            let formDataJson = \"\";\n            try {\n                formDataJson = JSON.stringify(data);\n            } catch (_a) {}\n            const flattenFormValues = flatten(control._formValues);\n            for(const key in flattenFormValues){\n                formData.append(key, flattenFormValues[key]);\n            }\n            if (onSubmit) {\n                await onSubmit({\n                    data,\n                    event,\n                    method,\n                    formData,\n                    formDataJson\n                });\n            }\n            if (action) {\n                try {\n                    const shouldStringifySubmissionData = [\n                        headers && headers[\"Content-Type\"],\n                        encType\n                    ].some((value1)=>value1 && value1.includes(\"json\"));\n                    const response = await fetch(String(action), {\n                        method,\n                        headers: {\n                            ...headers,\n                            ...encType ? {\n                                \"Content-Type\": encType\n                            } : {}\n                        },\n                        body: shouldStringifySubmissionData ? formDataJson : formData\n                    });\n                    if (response && (validateStatus ? !validateStatus(response.status) : response.status < 200 || response.status >= 300)) {\n                        hasError = true;\n                        onError && onError({\n                            response\n                        });\n                        type = String(response.status);\n                    } else {\n                        onSuccess && onSuccess({\n                            response\n                        });\n                    }\n                } catch (error) {\n                    hasError = true;\n                    onError && onError({\n                        error\n                    });\n                }\n            }\n        })(event);\n        if (hasError && props.control) {\n            props.control._subjects.state.next({\n                isSubmitSuccessful: false\n            });\n            props.control.setError(\"root.server\", {\n                type\n            });\n        }\n    };\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        setMounted(true);\n    }, []);\n    return render ? /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(react__WEBPACK_IMPORTED_MODULE_0__.Fragment, null, render({\n        submit\n    })) : /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"form\", {\n        noValidate: mounted,\n        action: action,\n        method: method,\n        encType: encType,\n        onSubmit: submit,\n        ...rest\n    }, children);\n}\nvar appendErrors = (name, validateAllFieldCriteria, errors, type, message)=>validateAllFieldCriteria ? {\n        ...errors[name],\n        types: {\n            ...errors[name] && errors[name].types ? errors[name].types : {},\n            [type]: message || true\n        }\n    } : {};\nvar generateId = ()=>{\n    const d = typeof performance === \"undefined\" ? Date.now() : performance.now() * 1000;\n    return \"xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx\".replace(/[xy]/g, (c)=>{\n        const r = (Math.random() * 16 + d) % 16 | 0;\n        return (c == \"x\" ? r : r & 0x3 | 0x8).toString(16);\n    });\n};\nvar getFocusFieldName = (name, index, options = {})=>options.shouldFocus || isUndefined(options.shouldFocus) ? options.focusName || `${name}.${isUndefined(options.focusIndex) ? index : options.focusIndex}.` : \"\";\nvar getValidationModes = (mode)=>({\n        isOnSubmit: !mode || mode === VALIDATION_MODE.onSubmit,\n        isOnBlur: mode === VALIDATION_MODE.onBlur,\n        isOnChange: mode === VALIDATION_MODE.onChange,\n        isOnAll: mode === VALIDATION_MODE.all,\n        isOnTouch: mode === VALIDATION_MODE.onTouched\n    });\nvar isWatched = (name, _names, isBlurEvent)=>!isBlurEvent && (_names.watchAll || _names.watch.has(name) || [\n        ..._names.watch\n    ].some((watchName)=>name.startsWith(watchName) && /^\\.\\w+/.test(name.slice(watchName.length))));\nconst iterateFieldsByAction = (fields, action, fieldsNames, abortEarly)=>{\n    for (const key of fieldsNames || Object.keys(fields)){\n        const field = get(fields, key);\n        if (field) {\n            const { _f, ...currentField } = field;\n            if (_f) {\n                if (_f.refs && _f.refs[0] && action(_f.refs[0], key) && !abortEarly) {\n                    return true;\n                } else if (_f.ref && action(_f.ref, _f.name) && !abortEarly) {\n                    return true;\n                } else {\n                    if (iterateFieldsByAction(currentField, action)) {\n                        break;\n                    }\n                }\n            } else if (isObject(currentField)) {\n                if (iterateFieldsByAction(currentField, action)) {\n                    break;\n                }\n            }\n        }\n    }\n    return;\n};\nvar updateFieldArrayRootError = (errors, error, name)=>{\n    const fieldArrayErrors = convertToArrayPayload(get(errors, name));\n    set(fieldArrayErrors, \"root\", error[name]);\n    set(errors, name, fieldArrayErrors);\n    return errors;\n};\nvar isFileInput = (element)=>element.type === \"file\";\nvar isFunction = (value1)=>typeof value1 === \"function\";\nvar isHTMLElement = (value1)=>{\n    if (!isWeb) {\n        return false;\n    }\n    const owner = value1 ? value1.ownerDocument : 0;\n    return value1 instanceof (owner && owner.defaultView ? owner.defaultView.HTMLElement : HTMLElement);\n};\nvar isMessage = (value1)=>isString(value1);\nvar isRadioInput = (element)=>element.type === \"radio\";\nvar isRegex = (value1)=>value1 instanceof RegExp;\nconst defaultResult = {\n    value: false,\n    isValid: false\n};\nconst validResult = {\n    value: true,\n    isValid: true\n};\nvar getCheckboxValue = (options)=>{\n    if (Array.isArray(options)) {\n        if (options.length > 1) {\n            const values = options.filter((option)=>option && option.checked && !option.disabled).map((option)=>option.value);\n            return {\n                value: values,\n                isValid: !!values.length\n            };\n        }\n        return options[0].checked && !options[0].disabled ? options[0].attributes && !isUndefined(options[0].attributes.value) ? isUndefined(options[0].value) || options[0].value === \"\" ? validResult : {\n            value: options[0].value,\n            isValid: true\n        } : validResult : defaultResult;\n    }\n    return defaultResult;\n};\nconst defaultReturn = {\n    isValid: false,\n    value: null\n};\nvar getRadioValue = (options)=>Array.isArray(options) ? options.reduce((previous, option)=>option && option.checked && !option.disabled ? {\n            isValid: true,\n            value: option.value\n        } : previous, defaultReturn) : defaultReturn;\nfunction getValidateError(result, ref, type = \"validate\") {\n    if (isMessage(result) || Array.isArray(result) && result.every(isMessage) || isBoolean(result) && !result) {\n        return {\n            type,\n            message: isMessage(result) ? result : \"\",\n            ref\n        };\n    }\n}\nvar getValueAndMessage = (validationData)=>isObject(validationData) && !isRegex(validationData) ? validationData : {\n        value: validationData,\n        message: \"\"\n    };\nvar validateField = async (field, disabledFieldNames, formValues, validateAllFieldCriteria, shouldUseNativeValidation, isFieldArray)=>{\n    const { ref, refs, required, maxLength, minLength, min, max, pattern, validate, name, valueAsNumber, mount } = field._f;\n    const inputValue = get(formValues, name);\n    if (!mount || disabledFieldNames.has(name)) {\n        return {};\n    }\n    const inputRef = refs ? refs[0] : ref;\n    const setCustomValidity = (message)=>{\n        if (shouldUseNativeValidation && inputRef.reportValidity) {\n            inputRef.setCustomValidity(isBoolean(message) ? \"\" : message || \"\");\n            inputRef.reportValidity();\n        }\n    };\n    const error = {};\n    const isRadio = isRadioInput(ref);\n    const isCheckBox = isCheckBoxInput(ref);\n    const isRadioOrCheckbox = isRadio || isCheckBox;\n    const isEmpty = (valueAsNumber || isFileInput(ref)) && isUndefined(ref.value) && isUndefined(inputValue) || isHTMLElement(ref) && ref.value === \"\" || inputValue === \"\" || Array.isArray(inputValue) && !inputValue.length;\n    const appendErrorsCurry = appendErrors.bind(null, name, validateAllFieldCriteria, error);\n    const getMinMaxMessage = (exceedMax, maxLengthMessage, minLengthMessage, maxType = INPUT_VALIDATION_RULES.maxLength, minType = INPUT_VALIDATION_RULES.minLength)=>{\n        const message = exceedMax ? maxLengthMessage : minLengthMessage;\n        error[name] = {\n            type: exceedMax ? maxType : minType,\n            message,\n            ref,\n            ...appendErrorsCurry(exceedMax ? maxType : minType, message)\n        };\n    };\n    if (isFieldArray ? !Array.isArray(inputValue) || !inputValue.length : required && (!isRadioOrCheckbox && (isEmpty || isNullOrUndefined(inputValue)) || isBoolean(inputValue) && !inputValue || isCheckBox && !getCheckboxValue(refs).isValid || isRadio && !getRadioValue(refs).isValid)) {\n        const { value: value1, message } = isMessage(required) ? {\n            value: !!required,\n            message: required\n        } : getValueAndMessage(required);\n        if (value1) {\n            error[name] = {\n                type: INPUT_VALIDATION_RULES.required,\n                message,\n                ref: inputRef,\n                ...appendErrorsCurry(INPUT_VALIDATION_RULES.required, message)\n            };\n            if (!validateAllFieldCriteria) {\n                setCustomValidity(message);\n                return error;\n            }\n        }\n    }\n    if (!isEmpty && (!isNullOrUndefined(min) || !isNullOrUndefined(max))) {\n        let exceedMax;\n        let exceedMin;\n        const maxOutput = getValueAndMessage(max);\n        const minOutput = getValueAndMessage(min);\n        if (!isNullOrUndefined(inputValue) && !isNaN(inputValue)) {\n            const valueNumber = ref.valueAsNumber || (inputValue ? +inputValue : inputValue);\n            if (!isNullOrUndefined(maxOutput.value)) {\n                exceedMax = valueNumber > maxOutput.value;\n            }\n            if (!isNullOrUndefined(minOutput.value)) {\n                exceedMin = valueNumber < minOutput.value;\n            }\n        } else {\n            const valueDate = ref.valueAsDate || new Date(inputValue);\n            const convertTimeToDate = (time)=>new Date(new Date().toDateString() + \" \" + time);\n            const isTime = ref.type == \"time\";\n            const isWeek = ref.type == \"week\";\n            if (isString(maxOutput.value) && inputValue) {\n                exceedMax = isTime ? convertTimeToDate(inputValue) > convertTimeToDate(maxOutput.value) : isWeek ? inputValue > maxOutput.value : valueDate > new Date(maxOutput.value);\n            }\n            if (isString(minOutput.value) && inputValue) {\n                exceedMin = isTime ? convertTimeToDate(inputValue) < convertTimeToDate(minOutput.value) : isWeek ? inputValue < minOutput.value : valueDate < new Date(minOutput.value);\n            }\n        }\n        if (exceedMax || exceedMin) {\n            getMinMaxMessage(!!exceedMax, maxOutput.message, minOutput.message, INPUT_VALIDATION_RULES.max, INPUT_VALIDATION_RULES.min);\n            if (!validateAllFieldCriteria) {\n                setCustomValidity(error[name].message);\n                return error;\n            }\n        }\n    }\n    if ((maxLength || minLength) && !isEmpty && (isString(inputValue) || isFieldArray && Array.isArray(inputValue))) {\n        const maxLengthOutput = getValueAndMessage(maxLength);\n        const minLengthOutput = getValueAndMessage(minLength);\n        const exceedMax = !isNullOrUndefined(maxLengthOutput.value) && inputValue.length > +maxLengthOutput.value;\n        const exceedMin = !isNullOrUndefined(minLengthOutput.value) && inputValue.length < +minLengthOutput.value;\n        if (exceedMax || exceedMin) {\n            getMinMaxMessage(exceedMax, maxLengthOutput.message, minLengthOutput.message);\n            if (!validateAllFieldCriteria) {\n                setCustomValidity(error[name].message);\n                return error;\n            }\n        }\n    }\n    if (pattern && !isEmpty && isString(inputValue)) {\n        const { value: patternValue, message } = getValueAndMessage(pattern);\n        if (isRegex(patternValue) && !inputValue.match(patternValue)) {\n            error[name] = {\n                type: INPUT_VALIDATION_RULES.pattern,\n                message,\n                ref,\n                ...appendErrorsCurry(INPUT_VALIDATION_RULES.pattern, message)\n            };\n            if (!validateAllFieldCriteria) {\n                setCustomValidity(message);\n                return error;\n            }\n        }\n    }\n    if (validate) {\n        if (isFunction(validate)) {\n            const result = await validate(inputValue, formValues);\n            const validateError = getValidateError(result, inputRef);\n            if (validateError) {\n                error[name] = {\n                    ...validateError,\n                    ...appendErrorsCurry(INPUT_VALIDATION_RULES.validate, validateError.message)\n                };\n                if (!validateAllFieldCriteria) {\n                    setCustomValidity(validateError.message);\n                    return error;\n                }\n            }\n        } else if (isObject(validate)) {\n            let validationResult = {};\n            for(const key in validate){\n                if (!isEmptyObject(validationResult) && !validateAllFieldCriteria) {\n                    break;\n                }\n                const validateError = getValidateError(await validate[key](inputValue, formValues), inputRef, key);\n                if (validateError) {\n                    validationResult = {\n                        ...validateError,\n                        ...appendErrorsCurry(key, validateError.message)\n                    };\n                    setCustomValidity(validateError.message);\n                    if (validateAllFieldCriteria) {\n                        error[name] = validationResult;\n                    }\n                }\n            }\n            if (!isEmptyObject(validationResult)) {\n                error[name] = {\n                    ref: inputRef,\n                    ...validationResult\n                };\n                if (!validateAllFieldCriteria) {\n                    return error;\n                }\n            }\n        }\n    }\n    setCustomValidity(true);\n    return error;\n};\nvar appendAt = (data, value1)=>[\n        ...data,\n        ...convertToArrayPayload(value1)\n    ];\nvar fillEmptyArray = (value1)=>Array.isArray(value1) ? value1.map(()=>undefined) : undefined;\nfunction insert(data, index, value1) {\n    return [\n        ...data.slice(0, index),\n        ...convertToArrayPayload(value1),\n        ...data.slice(index)\n    ];\n}\nvar moveArrayAt = (data, from, to)=>{\n    if (!Array.isArray(data)) {\n        return [];\n    }\n    if (isUndefined(data[to])) {\n        data[to] = undefined;\n    }\n    data.splice(to, 0, data.splice(from, 1)[0]);\n    return data;\n};\nvar prependAt = (data, value1)=>[\n        ...convertToArrayPayload(value1),\n        ...convertToArrayPayload(data)\n    ];\nfunction removeAtIndexes(data, indexes) {\n    let i = 0;\n    const temp = [\n        ...data\n    ];\n    for (const index of indexes){\n        temp.splice(index - i, 1);\n        i++;\n    }\n    return compact(temp).length ? temp : [];\n}\nvar removeArrayAt = (data, index)=>isUndefined(index) ? [] : removeAtIndexes(data, convertToArrayPayload(index).sort((a, b)=>a - b));\nvar swapArrayAt = (data, indexA, indexB)=>{\n    [data[indexA], data[indexB]] = [\n        data[indexB],\n        data[indexA]\n    ];\n};\nfunction baseGet(object, updatePath) {\n    const length = updatePath.slice(0, -1).length;\n    let index = 0;\n    while(index < length){\n        object = isUndefined(object) ? index++ : object[updatePath[index++]];\n    }\n    return object;\n}\nfunction isEmptyArray(obj) {\n    for(const key in obj){\n        if (obj.hasOwnProperty(key) && !isUndefined(obj[key])) {\n            return false;\n        }\n    }\n    return true;\n}\nfunction unset(object, path) {\n    const paths = Array.isArray(path) ? path : isKey(path) ? [\n        path\n    ] : stringToPath(path);\n    const childObject = paths.length === 1 ? object : baseGet(object, paths);\n    const index = paths.length - 1;\n    const key = paths[index];\n    if (childObject) {\n        delete childObject[key];\n    }\n    if (index !== 0 && (isObject(childObject) && isEmptyObject(childObject) || Array.isArray(childObject) && isEmptyArray(childObject))) {\n        unset(object, paths.slice(0, -1));\n    }\n    return object;\n}\nvar updateAt = (fieldValues, index, value1)=>{\n    fieldValues[index] = value1;\n    return fieldValues;\n};\n/**\n * A custom hook that exposes convenient methods to perform operations with a list of dynamic inputs that need to be appended, updated, removed etc. • [Demo](https://codesandbox.io/s/react-hook-form-usefieldarray-ssugn) • [Video](https://youtu.be/4MrbfGSFY2A)\n *\n * @remarks\n * [API](https://react-hook-form.com/docs/usefieldarray) • [Demo](https://codesandbox.io/s/react-hook-form-usefieldarray-ssugn)\n *\n * @param props - useFieldArray props\n *\n * @returns methods - functions to manipulate with the Field Arrays (dynamic inputs) {@link UseFieldArrayReturn}\n *\n * @example\n * ```tsx\n * function App() {\n *   const { register, control, handleSubmit, reset, trigger, setError } = useForm({\n *     defaultValues: {\n *       test: []\n *     }\n *   });\n *   const { fields, append } = useFieldArray({\n *     control,\n *     name: \"test\"\n *   });\n *\n *   return (\n *     <form onSubmit={handleSubmit(data => console.log(data))}>\n *       {fields.map((item, index) => (\n *          <input key={item.id} {...register(`test.${index}.firstName`)}  />\n *       ))}\n *       <button type=\"button\" onClick={() => append({ firstName: \"bill\" })}>\n *         append\n *       </button>\n *       <input type=\"submit\" />\n *     </form>\n *   );\n * }\n * ```\n */ function useFieldArray(props) {\n    const methods = useFormContext();\n    const { control = methods.control, name, keyName = \"id\", shouldUnregister, rules } = props;\n    const [fields, setFields] = react__WEBPACK_IMPORTED_MODULE_0__.useState(control._getFieldArray(name));\n    const ids = react__WEBPACK_IMPORTED_MODULE_0__.useRef(control._getFieldArray(name).map(generateId));\n    const _fieldIds = react__WEBPACK_IMPORTED_MODULE_0__.useRef(fields);\n    const _name = react__WEBPACK_IMPORTED_MODULE_0__.useRef(name);\n    const _actioned = react__WEBPACK_IMPORTED_MODULE_0__.useRef(false);\n    _name.current = name;\n    _fieldIds.current = fields;\n    control._names.array.add(name);\n    rules && control.register(name, rules);\n    useSubscribe({\n        next: ({ values, name: fieldArrayName })=>{\n            if (fieldArrayName === _name.current || !fieldArrayName) {\n                const fieldValues = get(values, _name.current);\n                if (Array.isArray(fieldValues)) {\n                    setFields(fieldValues);\n                    ids.current = fieldValues.map(generateId);\n                }\n            }\n        },\n        subject: control._subjects.array\n    });\n    const updateValues = react__WEBPACK_IMPORTED_MODULE_0__.useCallback((updatedFieldArrayValues)=>{\n        _actioned.current = true;\n        control._updateFieldArray(name, updatedFieldArrayValues);\n    }, [\n        control,\n        name\n    ]);\n    const append = (value1, options)=>{\n        const appendValue = convertToArrayPayload(cloneObject(value1));\n        const updatedFieldArrayValues = appendAt(control._getFieldArray(name), appendValue);\n        control._names.focus = getFocusFieldName(name, updatedFieldArrayValues.length - 1, options);\n        ids.current = appendAt(ids.current, appendValue.map(generateId));\n        updateValues(updatedFieldArrayValues);\n        setFields(updatedFieldArrayValues);\n        control._updateFieldArray(name, updatedFieldArrayValues, appendAt, {\n            argA: fillEmptyArray(value1)\n        });\n    };\n    const prepend = (value1, options)=>{\n        const prependValue = convertToArrayPayload(cloneObject(value1));\n        const updatedFieldArrayValues = prependAt(control._getFieldArray(name), prependValue);\n        control._names.focus = getFocusFieldName(name, 0, options);\n        ids.current = prependAt(ids.current, prependValue.map(generateId));\n        updateValues(updatedFieldArrayValues);\n        setFields(updatedFieldArrayValues);\n        control._updateFieldArray(name, updatedFieldArrayValues, prependAt, {\n            argA: fillEmptyArray(value1)\n        });\n    };\n    const remove = (index)=>{\n        const updatedFieldArrayValues = removeArrayAt(control._getFieldArray(name), index);\n        ids.current = removeArrayAt(ids.current, index);\n        updateValues(updatedFieldArrayValues);\n        setFields(updatedFieldArrayValues);\n        !Array.isArray(get(control._fields, name)) && set(control._fields, name, undefined);\n        control._updateFieldArray(name, updatedFieldArrayValues, removeArrayAt, {\n            argA: index\n        });\n    };\n    const insert$1 = (index, value1, options)=>{\n        const insertValue = convertToArrayPayload(cloneObject(value1));\n        const updatedFieldArrayValues = insert(control._getFieldArray(name), index, insertValue);\n        control._names.focus = getFocusFieldName(name, index, options);\n        ids.current = insert(ids.current, index, insertValue.map(generateId));\n        updateValues(updatedFieldArrayValues);\n        setFields(updatedFieldArrayValues);\n        control._updateFieldArray(name, updatedFieldArrayValues, insert, {\n            argA: index,\n            argB: fillEmptyArray(value1)\n        });\n    };\n    const swap = (indexA, indexB)=>{\n        const updatedFieldArrayValues = control._getFieldArray(name);\n        swapArrayAt(updatedFieldArrayValues, indexA, indexB);\n        swapArrayAt(ids.current, indexA, indexB);\n        updateValues(updatedFieldArrayValues);\n        setFields(updatedFieldArrayValues);\n        control._updateFieldArray(name, updatedFieldArrayValues, swapArrayAt, {\n            argA: indexA,\n            argB: indexB\n        }, false);\n    };\n    const move = (from, to)=>{\n        const updatedFieldArrayValues = control._getFieldArray(name);\n        moveArrayAt(updatedFieldArrayValues, from, to);\n        moveArrayAt(ids.current, from, to);\n        updateValues(updatedFieldArrayValues);\n        setFields(updatedFieldArrayValues);\n        control._updateFieldArray(name, updatedFieldArrayValues, moveArrayAt, {\n            argA: from,\n            argB: to\n        }, false);\n    };\n    const update = (index, value1)=>{\n        const updateValue = cloneObject(value1);\n        const updatedFieldArrayValues = updateAt(control._getFieldArray(name), index, updateValue);\n        ids.current = [\n            ...updatedFieldArrayValues\n        ].map((item, i)=>!item || i === index ? generateId() : ids.current[i]);\n        updateValues(updatedFieldArrayValues);\n        setFields([\n            ...updatedFieldArrayValues\n        ]);\n        control._updateFieldArray(name, updatedFieldArrayValues, updateAt, {\n            argA: index,\n            argB: updateValue\n        }, true, false);\n    };\n    const replace = (value1)=>{\n        const updatedFieldArrayValues = convertToArrayPayload(cloneObject(value1));\n        ids.current = updatedFieldArrayValues.map(generateId);\n        updateValues([\n            ...updatedFieldArrayValues\n        ]);\n        setFields([\n            ...updatedFieldArrayValues\n        ]);\n        control._updateFieldArray(name, [\n            ...updatedFieldArrayValues\n        ], (data)=>data, {}, true, false);\n    };\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        control._state.action = false;\n        isWatched(name, control._names) && control._subjects.state.next({\n            ...control._formState\n        });\n        if (_actioned.current && (!getValidationModes(control._options.mode).isOnSubmit || control._formState.isSubmitted)) {\n            if (control._options.resolver) {\n                control._executeSchema([\n                    name\n                ]).then((result)=>{\n                    const error = get(result.errors, name);\n                    const existingError = get(control._formState.errors, name);\n                    if (existingError ? !error && existingError.type || error && (existingError.type !== error.type || existingError.message !== error.message) : error && error.type) {\n                        error ? set(control._formState.errors, name, error) : unset(control._formState.errors, name);\n                        control._subjects.state.next({\n                            errors: control._formState.errors\n                        });\n                    }\n                });\n            } else {\n                const field = get(control._fields, name);\n                if (field && field._f && !(getValidationModes(control._options.reValidateMode).isOnSubmit && getValidationModes(control._options.mode).isOnSubmit)) {\n                    validateField(field, control._names.disabled, control._formValues, control._options.criteriaMode === VALIDATION_MODE.all, control._options.shouldUseNativeValidation, true).then((error)=>!isEmptyObject(error) && control._subjects.state.next({\n                            errors: updateFieldArrayRootError(control._formState.errors, error, name)\n                        }));\n                }\n            }\n        }\n        control._subjects.values.next({\n            name,\n            values: {\n                ...control._formValues\n            }\n        });\n        control._names.focus && iterateFieldsByAction(control._fields, (ref, key)=>{\n            if (control._names.focus && key.startsWith(control._names.focus) && ref.focus) {\n                ref.focus();\n                return 1;\n            }\n            return;\n        });\n        control._names.focus = \"\";\n        control._updateValid();\n        _actioned.current = false;\n    }, [\n        fields,\n        name,\n        control\n    ]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        !get(control._formValues, name) && control._updateFieldArray(name);\n        return ()=>{\n            (control._options.shouldUnregister || shouldUnregister) && control.unregister(name);\n        };\n    }, [\n        name,\n        control,\n        keyName,\n        shouldUnregister\n    ]);\n    return {\n        swap: react__WEBPACK_IMPORTED_MODULE_0__.useCallback(swap, [\n            updateValues,\n            name,\n            control\n        ]),\n        move: react__WEBPACK_IMPORTED_MODULE_0__.useCallback(move, [\n            updateValues,\n            name,\n            control\n        ]),\n        prepend: react__WEBPACK_IMPORTED_MODULE_0__.useCallback(prepend, [\n            updateValues,\n            name,\n            control\n        ]),\n        append: react__WEBPACK_IMPORTED_MODULE_0__.useCallback(append, [\n            updateValues,\n            name,\n            control\n        ]),\n        remove: react__WEBPACK_IMPORTED_MODULE_0__.useCallback(remove, [\n            updateValues,\n            name,\n            control\n        ]),\n        insert: react__WEBPACK_IMPORTED_MODULE_0__.useCallback(insert$1, [\n            updateValues,\n            name,\n            control\n        ]),\n        update: react__WEBPACK_IMPORTED_MODULE_0__.useCallback(update, [\n            updateValues,\n            name,\n            control\n        ]),\n        replace: react__WEBPACK_IMPORTED_MODULE_0__.useCallback(replace, [\n            updateValues,\n            name,\n            control\n        ]),\n        fields: react__WEBPACK_IMPORTED_MODULE_0__.useMemo(()=>fields.map((field, index)=>({\n                    ...field,\n                    [keyName]: ids.current[index] || generateId()\n                })), [\n            fields,\n            keyName\n        ])\n    };\n}\nvar createSubject = ()=>{\n    let _observers = [];\n    const next = (value1)=>{\n        for (const observer of _observers){\n            observer.next && observer.next(value1);\n        }\n    };\n    const subscribe = (observer)=>{\n        _observers.push(observer);\n        return {\n            unsubscribe: ()=>{\n                _observers = _observers.filter((o)=>o !== observer);\n            }\n        };\n    };\n    const unsubscribe = ()=>{\n        _observers = [];\n    };\n    return {\n        get observers () {\n            return _observers;\n        },\n        next,\n        subscribe,\n        unsubscribe\n    };\n};\nvar isPrimitive = (value1)=>isNullOrUndefined(value1) || !isObjectType(value1);\nfunction deepEqual(object1, object2) {\n    if (isPrimitive(object1) || isPrimitive(object2)) {\n        return object1 === object2;\n    }\n    if (isDateObject(object1) && isDateObject(object2)) {\n        return object1.getTime() === object2.getTime();\n    }\n    const keys1 = Object.keys(object1);\n    const keys2 = Object.keys(object2);\n    if (keys1.length !== keys2.length) {\n        return false;\n    }\n    for (const key of keys1){\n        const val1 = object1[key];\n        if (!keys2.includes(key)) {\n            return false;\n        }\n        if (key !== \"ref\") {\n            const val2 = object2[key];\n            if (isDateObject(val1) && isDateObject(val2) || isObject(val1) && isObject(val2) || Array.isArray(val1) && Array.isArray(val2) ? !deepEqual(val1, val2) : val1 !== val2) {\n                return false;\n            }\n        }\n    }\n    return true;\n}\nvar isMultipleSelect = (element)=>element.type === `select-multiple`;\nvar isRadioOrCheckbox = (ref)=>isRadioInput(ref) || isCheckBoxInput(ref);\nvar live = (ref)=>isHTMLElement(ref) && ref.isConnected;\nvar objectHasFunction = (data)=>{\n    for(const key in data){\n        if (isFunction(data[key])) {\n            return true;\n        }\n    }\n    return false;\n};\nfunction markFieldsDirty(data, fields = {}) {\n    const isParentNodeArray = Array.isArray(data);\n    if (isObject(data) || isParentNodeArray) {\n        for(const key in data){\n            if (Array.isArray(data[key]) || isObject(data[key]) && !objectHasFunction(data[key])) {\n                fields[key] = Array.isArray(data[key]) ? [] : {};\n                markFieldsDirty(data[key], fields[key]);\n            } else if (!isNullOrUndefined(data[key])) {\n                fields[key] = true;\n            }\n        }\n    }\n    return fields;\n}\nfunction getDirtyFieldsFromDefaultValues(data, formValues, dirtyFieldsFromValues) {\n    const isParentNodeArray = Array.isArray(data);\n    if (isObject(data) || isParentNodeArray) {\n        for(const key in data){\n            if (Array.isArray(data[key]) || isObject(data[key]) && !objectHasFunction(data[key])) {\n                if (isUndefined(formValues) || isPrimitive(dirtyFieldsFromValues[key])) {\n                    dirtyFieldsFromValues[key] = Array.isArray(data[key]) ? markFieldsDirty(data[key], []) : {\n                        ...markFieldsDirty(data[key])\n                    };\n                } else {\n                    getDirtyFieldsFromDefaultValues(data[key], isNullOrUndefined(formValues) ? {} : formValues[key], dirtyFieldsFromValues[key]);\n                }\n            } else {\n                dirtyFieldsFromValues[key] = !deepEqual(data[key], formValues[key]);\n            }\n        }\n    }\n    return dirtyFieldsFromValues;\n}\nvar getDirtyFields = (defaultValues, formValues)=>getDirtyFieldsFromDefaultValues(defaultValues, formValues, markFieldsDirty(formValues));\nvar getFieldValueAs = (value1, { valueAsNumber, valueAsDate, setValueAs })=>isUndefined(value1) ? value1 : valueAsNumber ? value1 === \"\" ? NaN : value1 ? +value1 : value1 : valueAsDate && isString(value1) ? new Date(value1) : setValueAs ? setValueAs(value1) : value1;\nfunction getFieldValue(_f) {\n    const ref = _f.ref;\n    if (isFileInput(ref)) {\n        return ref.files;\n    }\n    if (isRadioInput(ref)) {\n        return getRadioValue(_f.refs).value;\n    }\n    if (isMultipleSelect(ref)) {\n        return [\n            ...ref.selectedOptions\n        ].map(({ value: value1 })=>value1);\n    }\n    if (isCheckBoxInput(ref)) {\n        return getCheckboxValue(_f.refs).value;\n    }\n    return getFieldValueAs(isUndefined(ref.value) ? _f.ref.value : ref.value, _f);\n}\nvar getResolverOptions = (fieldsNames, _fields, criteriaMode, shouldUseNativeValidation)=>{\n    const fields = {};\n    for (const name of fieldsNames){\n        const field = get(_fields, name);\n        field && set(fields, name, field._f);\n    }\n    return {\n        criteriaMode,\n        names: [\n            ...fieldsNames\n        ],\n        fields,\n        shouldUseNativeValidation\n    };\n};\nvar getRuleValue = (rule)=>isUndefined(rule) ? rule : isRegex(rule) ? rule.source : isObject(rule) ? isRegex(rule.value) ? rule.value.source : rule.value : rule;\nconst ASYNC_FUNCTION = \"AsyncFunction\";\nvar hasPromiseValidation = (fieldReference)=>!!fieldReference && !!fieldReference.validate && !!(isFunction(fieldReference.validate) && fieldReference.validate.constructor.name === ASYNC_FUNCTION || isObject(fieldReference.validate) && Object.values(fieldReference.validate).find((validateFunction)=>validateFunction.constructor.name === ASYNC_FUNCTION));\nvar hasValidation = (options)=>options.mount && (options.required || options.min || options.max || options.maxLength || options.minLength || options.pattern || options.validate);\nfunction schemaErrorLookup(errors, _fields, name) {\n    const error = get(errors, name);\n    if (error || isKey(name)) {\n        return {\n            error,\n            name\n        };\n    }\n    const names = name.split(\".\");\n    while(names.length){\n        const fieldName = names.join(\".\");\n        const field = get(_fields, fieldName);\n        const foundError = get(errors, fieldName);\n        if (field && !Array.isArray(field) && name !== fieldName) {\n            return {\n                name\n            };\n        }\n        if (foundError && foundError.type) {\n            return {\n                name: fieldName,\n                error: foundError\n            };\n        }\n        names.pop();\n    }\n    return {\n        name\n    };\n}\nvar skipValidation = (isBlurEvent, isTouched, isSubmitted, reValidateMode, mode)=>{\n    if (mode.isOnAll) {\n        return false;\n    } else if (!isSubmitted && mode.isOnTouch) {\n        return !(isTouched || isBlurEvent);\n    } else if (isSubmitted ? reValidateMode.isOnBlur : mode.isOnBlur) {\n        return !isBlurEvent;\n    } else if (isSubmitted ? reValidateMode.isOnChange : mode.isOnChange) {\n        return isBlurEvent;\n    }\n    return true;\n};\nvar unsetEmptyArray = (ref, name)=>!compact(get(ref, name)).length && unset(ref, name);\nconst defaultOptions = {\n    mode: VALIDATION_MODE.onSubmit,\n    reValidateMode: VALIDATION_MODE.onChange,\n    shouldFocusError: true\n};\nfunction createFormControl(props = {}) {\n    let _options = {\n        ...defaultOptions,\n        ...props\n    };\n    let _formState = {\n        submitCount: 0,\n        isDirty: false,\n        isLoading: isFunction(_options.defaultValues),\n        isValidating: false,\n        isSubmitted: false,\n        isSubmitting: false,\n        isSubmitSuccessful: false,\n        isValid: false,\n        touchedFields: {},\n        dirtyFields: {},\n        validatingFields: {},\n        errors: _options.errors || {},\n        disabled: _options.disabled || false\n    };\n    let _fields = {};\n    let _defaultValues = isObject(_options.defaultValues) || isObject(_options.values) ? cloneObject(_options.defaultValues || _options.values) || {} : {};\n    let _formValues = _options.shouldUnregister ? {} : cloneObject(_defaultValues);\n    let _state = {\n        action: false,\n        mount: false,\n        watch: false\n    };\n    let _names = {\n        mount: new Set(),\n        disabled: new Set(),\n        unMount: new Set(),\n        array: new Set(),\n        watch: new Set()\n    };\n    let delayErrorCallback;\n    let timer = 0;\n    const _proxyFormState = {\n        isDirty: false,\n        dirtyFields: false,\n        validatingFields: false,\n        touchedFields: false,\n        isValidating: false,\n        isValid: false,\n        errors: false\n    };\n    const _subjects = {\n        values: createSubject(),\n        array: createSubject(),\n        state: createSubject()\n    };\n    const validationModeBeforeSubmit = getValidationModes(_options.mode);\n    const validationModeAfterSubmit = getValidationModes(_options.reValidateMode);\n    const shouldDisplayAllAssociatedErrors = _options.criteriaMode === VALIDATION_MODE.all;\n    const debounce = (callback)=>(wait)=>{\n            clearTimeout(timer);\n            timer = setTimeout(callback, wait);\n        };\n    const _updateValid = async (shouldUpdateValid)=>{\n        if (!_options.disabled && (_proxyFormState.isValid || shouldUpdateValid)) {\n            const isValid = _options.resolver ? isEmptyObject((await _executeSchema()).errors) : await executeBuiltInValidation(_fields, true);\n            if (isValid !== _formState.isValid) {\n                _subjects.state.next({\n                    isValid\n                });\n            }\n        }\n    };\n    const _updateIsValidating = (names, isValidating)=>{\n        if (!_options.disabled && (_proxyFormState.isValidating || _proxyFormState.validatingFields)) {\n            (names || Array.from(_names.mount)).forEach((name)=>{\n                if (name) {\n                    isValidating ? set(_formState.validatingFields, name, isValidating) : unset(_formState.validatingFields, name);\n                }\n            });\n            _subjects.state.next({\n                validatingFields: _formState.validatingFields,\n                isValidating: !isEmptyObject(_formState.validatingFields)\n            });\n        }\n    };\n    const _updateFieldArray = (name, values = [], method, args, shouldSetValues = true, shouldUpdateFieldsAndState = true)=>{\n        if (args && method && !_options.disabled) {\n            _state.action = true;\n            if (shouldUpdateFieldsAndState && Array.isArray(get(_fields, name))) {\n                const fieldValues = method(get(_fields, name), args.argA, args.argB);\n                shouldSetValues && set(_fields, name, fieldValues);\n            }\n            if (shouldUpdateFieldsAndState && Array.isArray(get(_formState.errors, name))) {\n                const errors = method(get(_formState.errors, name), args.argA, args.argB);\n                shouldSetValues && set(_formState.errors, name, errors);\n                unsetEmptyArray(_formState.errors, name);\n            }\n            if (_proxyFormState.touchedFields && shouldUpdateFieldsAndState && Array.isArray(get(_formState.touchedFields, name))) {\n                const touchedFields = method(get(_formState.touchedFields, name), args.argA, args.argB);\n                shouldSetValues && set(_formState.touchedFields, name, touchedFields);\n            }\n            if (_proxyFormState.dirtyFields) {\n                _formState.dirtyFields = getDirtyFields(_defaultValues, _formValues);\n            }\n            _subjects.state.next({\n                name,\n                isDirty: _getDirty(name, values),\n                dirtyFields: _formState.dirtyFields,\n                errors: _formState.errors,\n                isValid: _formState.isValid\n            });\n        } else {\n            set(_formValues, name, values);\n        }\n    };\n    const updateErrors = (name, error)=>{\n        set(_formState.errors, name, error);\n        _subjects.state.next({\n            errors: _formState.errors\n        });\n    };\n    const _setErrors = (errors)=>{\n        _formState.errors = errors;\n        _subjects.state.next({\n            errors: _formState.errors,\n            isValid: false\n        });\n    };\n    const updateValidAndValue = (name, shouldSkipSetValueAs, value1, ref)=>{\n        const field = get(_fields, name);\n        if (field) {\n            const defaultValue = get(_formValues, name, isUndefined(value1) ? get(_defaultValues, name) : value1);\n            isUndefined(defaultValue) || ref && ref.defaultChecked || shouldSkipSetValueAs ? set(_formValues, name, shouldSkipSetValueAs ? defaultValue : getFieldValue(field._f)) : setFieldValue(name, defaultValue);\n            _state.mount && _updateValid();\n        }\n    };\n    const updateTouchAndDirty = (name, fieldValue, isBlurEvent, shouldDirty, shouldRender)=>{\n        let shouldUpdateField = false;\n        let isPreviousDirty = false;\n        const output = {\n            name\n        };\n        if (!_options.disabled) {\n            const disabledField = !!(get(_fields, name) && get(_fields, name)._f && get(_fields, name)._f.disabled);\n            if (!isBlurEvent || shouldDirty) {\n                if (_proxyFormState.isDirty) {\n                    isPreviousDirty = _formState.isDirty;\n                    _formState.isDirty = output.isDirty = _getDirty();\n                    shouldUpdateField = isPreviousDirty !== output.isDirty;\n                }\n                const isCurrentFieldPristine = disabledField || deepEqual(get(_defaultValues, name), fieldValue);\n                isPreviousDirty = !!(!disabledField && get(_formState.dirtyFields, name));\n                isCurrentFieldPristine || disabledField ? unset(_formState.dirtyFields, name) : set(_formState.dirtyFields, name, true);\n                output.dirtyFields = _formState.dirtyFields;\n                shouldUpdateField = shouldUpdateField || _proxyFormState.dirtyFields && isPreviousDirty !== !isCurrentFieldPristine;\n            }\n            if (isBlurEvent) {\n                const isPreviousFieldTouched = get(_formState.touchedFields, name);\n                if (!isPreviousFieldTouched) {\n                    set(_formState.touchedFields, name, isBlurEvent);\n                    output.touchedFields = _formState.touchedFields;\n                    shouldUpdateField = shouldUpdateField || _proxyFormState.touchedFields && isPreviousFieldTouched !== isBlurEvent;\n                }\n            }\n            shouldUpdateField && shouldRender && _subjects.state.next(output);\n        }\n        return shouldUpdateField ? output : {};\n    };\n    const shouldRenderByError = (name, isValid, error, fieldState)=>{\n        const previousFieldError = get(_formState.errors, name);\n        const shouldUpdateValid = _proxyFormState.isValid && isBoolean(isValid) && _formState.isValid !== isValid;\n        if (_options.delayError && error) {\n            delayErrorCallback = debounce(()=>updateErrors(name, error));\n            delayErrorCallback(_options.delayError);\n        } else {\n            clearTimeout(timer);\n            delayErrorCallback = null;\n            error ? set(_formState.errors, name, error) : unset(_formState.errors, name);\n        }\n        if ((error ? !deepEqual(previousFieldError, error) : previousFieldError) || !isEmptyObject(fieldState) || shouldUpdateValid) {\n            const updatedFormState = {\n                ...fieldState,\n                ...shouldUpdateValid && isBoolean(isValid) ? {\n                    isValid\n                } : {},\n                errors: _formState.errors,\n                name\n            };\n            _formState = {\n                ..._formState,\n                ...updatedFormState\n            };\n            _subjects.state.next(updatedFormState);\n        }\n    };\n    const _executeSchema = async (name)=>{\n        _updateIsValidating(name, true);\n        const result = await _options.resolver(_formValues, _options.context, getResolverOptions(name || _names.mount, _fields, _options.criteriaMode, _options.shouldUseNativeValidation));\n        _updateIsValidating(name);\n        return result;\n    };\n    const executeSchemaAndUpdateState = async (names)=>{\n        const { errors } = await _executeSchema(names);\n        if (names) {\n            for (const name of names){\n                const error = get(errors, name);\n                error ? set(_formState.errors, name, error) : unset(_formState.errors, name);\n            }\n        } else {\n            _formState.errors = errors;\n        }\n        return errors;\n    };\n    const executeBuiltInValidation = async (fields, shouldOnlyCheckValid, context = {\n        valid: true\n    })=>{\n        for(const name in fields){\n            const field = fields[name];\n            if (field) {\n                const { _f, ...fieldValue } = field;\n                if (_f) {\n                    const isFieldArrayRoot = _names.array.has(_f.name);\n                    const isPromiseFunction = field._f && hasPromiseValidation(field._f);\n                    if (isPromiseFunction && _proxyFormState.validatingFields) {\n                        _updateIsValidating([\n                            name\n                        ], true);\n                    }\n                    const fieldError = await validateField(field, _names.disabled, _formValues, shouldDisplayAllAssociatedErrors, _options.shouldUseNativeValidation && !shouldOnlyCheckValid, isFieldArrayRoot);\n                    if (isPromiseFunction && _proxyFormState.validatingFields) {\n                        _updateIsValidating([\n                            name\n                        ]);\n                    }\n                    if (fieldError[_f.name]) {\n                        context.valid = false;\n                        if (shouldOnlyCheckValid) {\n                            break;\n                        }\n                    }\n                    !shouldOnlyCheckValid && (get(fieldError, _f.name) ? isFieldArrayRoot ? updateFieldArrayRootError(_formState.errors, fieldError, _f.name) : set(_formState.errors, _f.name, fieldError[_f.name]) : unset(_formState.errors, _f.name));\n                }\n                !isEmptyObject(fieldValue) && await executeBuiltInValidation(fieldValue, shouldOnlyCheckValid, context);\n            }\n        }\n        return context.valid;\n    };\n    const _removeUnmounted = ()=>{\n        for (const name of _names.unMount){\n            const field = get(_fields, name);\n            field && (field._f.refs ? field._f.refs.every((ref)=>!live(ref)) : !live(field._f.ref)) && unregister(name);\n        }\n        _names.unMount = new Set();\n    };\n    const _getDirty = (name, data)=>!_options.disabled && (name && data && set(_formValues, name, data), !deepEqual(getValues(), _defaultValues));\n    const _getWatch = (names, defaultValue, isGlobal)=>generateWatchOutput(names, _names, {\n            ..._state.mount ? _formValues : isUndefined(defaultValue) ? _defaultValues : isString(names) ? {\n                [names]: defaultValue\n            } : defaultValue\n        }, isGlobal, defaultValue);\n    const _getFieldArray = (name)=>compact(get(_state.mount ? _formValues : _defaultValues, name, _options.shouldUnregister ? get(_defaultValues, name, []) : []));\n    const setFieldValue = (name, value1, options = {})=>{\n        const field = get(_fields, name);\n        let fieldValue = value1;\n        if (field) {\n            const fieldReference = field._f;\n            if (fieldReference) {\n                !fieldReference.disabled && set(_formValues, name, getFieldValueAs(value1, fieldReference));\n                fieldValue = isHTMLElement(fieldReference.ref) && isNullOrUndefined(value1) ? \"\" : value1;\n                if (isMultipleSelect(fieldReference.ref)) {\n                    [\n                        ...fieldReference.ref.options\n                    ].forEach((optionRef)=>optionRef.selected = fieldValue.includes(optionRef.value));\n                } else if (fieldReference.refs) {\n                    if (isCheckBoxInput(fieldReference.ref)) {\n                        fieldReference.refs.length > 1 ? fieldReference.refs.forEach((checkboxRef)=>(!checkboxRef.defaultChecked || !checkboxRef.disabled) && (checkboxRef.checked = Array.isArray(fieldValue) ? !!fieldValue.find((data)=>data === checkboxRef.value) : fieldValue === checkboxRef.value)) : fieldReference.refs[0] && (fieldReference.refs[0].checked = !!fieldValue);\n                    } else {\n                        fieldReference.refs.forEach((radioRef)=>radioRef.checked = radioRef.value === fieldValue);\n                    }\n                } else if (isFileInput(fieldReference.ref)) {\n                    fieldReference.ref.value = \"\";\n                } else {\n                    fieldReference.ref.value = fieldValue;\n                    if (!fieldReference.ref.type) {\n                        _subjects.values.next({\n                            name,\n                            values: {\n                                ..._formValues\n                            }\n                        });\n                    }\n                }\n            }\n        }\n        (options.shouldDirty || options.shouldTouch) && updateTouchAndDirty(name, fieldValue, options.shouldTouch, options.shouldDirty, true);\n        options.shouldValidate && trigger(name);\n    };\n    const setValues = (name, value1, options)=>{\n        for(const fieldKey in value1){\n            const fieldValue = value1[fieldKey];\n            const fieldName = `${name}.${fieldKey}`;\n            const field = get(_fields, fieldName);\n            (_names.array.has(name) || isObject(fieldValue) || field && !field._f) && !isDateObject(fieldValue) ? setValues(fieldName, fieldValue, options) : setFieldValue(fieldName, fieldValue, options);\n        }\n    };\n    const setValue = (name, value1, options = {})=>{\n        const field = get(_fields, name);\n        const isFieldArray = _names.array.has(name);\n        const cloneValue = cloneObject(value1);\n        set(_formValues, name, cloneValue);\n        if (isFieldArray) {\n            _subjects.array.next({\n                name,\n                values: {\n                    ..._formValues\n                }\n            });\n            if ((_proxyFormState.isDirty || _proxyFormState.dirtyFields) && options.shouldDirty) {\n                _subjects.state.next({\n                    name,\n                    dirtyFields: getDirtyFields(_defaultValues, _formValues),\n                    isDirty: _getDirty(name, cloneValue)\n                });\n            }\n        } else {\n            field && !field._f && !isNullOrUndefined(cloneValue) ? setValues(name, cloneValue, options) : setFieldValue(name, cloneValue, options);\n        }\n        isWatched(name, _names) && _subjects.state.next({\n            ..._formState\n        });\n        _subjects.values.next({\n            name: _state.mount ? name : undefined,\n            values: {\n                ..._formValues\n            }\n        });\n    };\n    const onChange = async (event)=>{\n        _state.mount = true;\n        const target = event.target;\n        let name = target.name;\n        let isFieldValueUpdated = true;\n        const field = get(_fields, name);\n        const getCurrentFieldValue = ()=>target.type ? getFieldValue(field._f) : getEventValue(event);\n        const _updateIsFieldValueUpdated = (fieldValue)=>{\n            isFieldValueUpdated = Number.isNaN(fieldValue) || isDateObject(fieldValue) && isNaN(fieldValue.getTime()) || deepEqual(fieldValue, get(_formValues, name, fieldValue));\n        };\n        if (field) {\n            let error;\n            let isValid;\n            const fieldValue = getCurrentFieldValue();\n            const isBlurEvent = event.type === EVENTS.BLUR || event.type === EVENTS.FOCUS_OUT;\n            const shouldSkipValidation = !hasValidation(field._f) && !_options.resolver && !get(_formState.errors, name) && !field._f.deps || skipValidation(isBlurEvent, get(_formState.touchedFields, name), _formState.isSubmitted, validationModeAfterSubmit, validationModeBeforeSubmit);\n            const watched = isWatched(name, _names, isBlurEvent);\n            set(_formValues, name, fieldValue);\n            if (isBlurEvent) {\n                field._f.onBlur && field._f.onBlur(event);\n                delayErrorCallback && delayErrorCallback(0);\n            } else if (field._f.onChange) {\n                field._f.onChange(event);\n            }\n            const fieldState = updateTouchAndDirty(name, fieldValue, isBlurEvent, false);\n            const shouldRender = !isEmptyObject(fieldState) || watched;\n            !isBlurEvent && _subjects.values.next({\n                name,\n                type: event.type,\n                values: {\n                    ..._formValues\n                }\n            });\n            if (shouldSkipValidation) {\n                if (_proxyFormState.isValid) {\n                    if (_options.mode === \"onBlur\" && isBlurEvent) {\n                        _updateValid();\n                    } else if (!isBlurEvent) {\n                        _updateValid();\n                    }\n                }\n                return shouldRender && _subjects.state.next({\n                    name,\n                    ...watched ? {} : fieldState\n                });\n            }\n            !isBlurEvent && watched && _subjects.state.next({\n                ..._formState\n            });\n            if (_options.resolver) {\n                const { errors } = await _executeSchema([\n                    name\n                ]);\n                _updateIsFieldValueUpdated(fieldValue);\n                if (isFieldValueUpdated) {\n                    const previousErrorLookupResult = schemaErrorLookup(_formState.errors, _fields, name);\n                    const errorLookupResult = schemaErrorLookup(errors, _fields, previousErrorLookupResult.name || name);\n                    error = errorLookupResult.error;\n                    name = errorLookupResult.name;\n                    isValid = isEmptyObject(errors);\n                }\n            } else {\n                _updateIsValidating([\n                    name\n                ], true);\n                error = (await validateField(field, _names.disabled, _formValues, shouldDisplayAllAssociatedErrors, _options.shouldUseNativeValidation))[name];\n                _updateIsValidating([\n                    name\n                ]);\n                _updateIsFieldValueUpdated(fieldValue);\n                if (isFieldValueUpdated) {\n                    if (error) {\n                        isValid = false;\n                    } else if (_proxyFormState.isValid) {\n                        isValid = await executeBuiltInValidation(_fields, true);\n                    }\n                }\n            }\n            if (isFieldValueUpdated) {\n                field._f.deps && trigger(field._f.deps);\n                shouldRenderByError(name, isValid, error, fieldState);\n            }\n        }\n    };\n    const _focusInput = (ref, key)=>{\n        if (get(_formState.errors, key) && ref.focus) {\n            ref.focus();\n            return 1;\n        }\n        return;\n    };\n    const trigger = async (name, options = {})=>{\n        let isValid;\n        let validationResult;\n        const fieldNames = convertToArrayPayload(name);\n        if (_options.resolver) {\n            const errors = await executeSchemaAndUpdateState(isUndefined(name) ? name : fieldNames);\n            isValid = isEmptyObject(errors);\n            validationResult = name ? !fieldNames.some((name)=>get(errors, name)) : isValid;\n        } else if (name) {\n            validationResult = (await Promise.all(fieldNames.map(async (fieldName)=>{\n                const field = get(_fields, fieldName);\n                return await executeBuiltInValidation(field && field._f ? {\n                    [fieldName]: field\n                } : field);\n            }))).every(Boolean);\n            !(!validationResult && !_formState.isValid) && _updateValid();\n        } else {\n            validationResult = isValid = await executeBuiltInValidation(_fields);\n        }\n        _subjects.state.next({\n            ...!isString(name) || _proxyFormState.isValid && isValid !== _formState.isValid ? {} : {\n                name\n            },\n            ..._options.resolver || !name ? {\n                isValid\n            } : {},\n            errors: _formState.errors\n        });\n        options.shouldFocus && !validationResult && iterateFieldsByAction(_fields, _focusInput, name ? fieldNames : _names.mount);\n        return validationResult;\n    };\n    const getValues = (fieldNames)=>{\n        const values = {\n            ..._state.mount ? _formValues : _defaultValues\n        };\n        return isUndefined(fieldNames) ? values : isString(fieldNames) ? get(values, fieldNames) : fieldNames.map((name)=>get(values, name));\n    };\n    const getFieldState = (name, formState)=>({\n            invalid: !!get((formState || _formState).errors, name),\n            isDirty: !!get((formState || _formState).dirtyFields, name),\n            error: get((formState || _formState).errors, name),\n            isValidating: !!get(_formState.validatingFields, name),\n            isTouched: !!get((formState || _formState).touchedFields, name)\n        });\n    const clearErrors = (name)=>{\n        name && convertToArrayPayload(name).forEach((inputName)=>unset(_formState.errors, inputName));\n        _subjects.state.next({\n            errors: name ? _formState.errors : {}\n        });\n    };\n    const setError = (name, error, options)=>{\n        const ref = (get(_fields, name, {\n            _f: {}\n        })._f || {}).ref;\n        const currentError = get(_formState.errors, name) || {};\n        // Don't override existing error messages elsewhere in the object tree.\n        const { ref: currentRef, message, type, ...restOfErrorTree } = currentError;\n        set(_formState.errors, name, {\n            ...restOfErrorTree,\n            ...error,\n            ref\n        });\n        _subjects.state.next({\n            name,\n            errors: _formState.errors,\n            isValid: false\n        });\n        options && options.shouldFocus && ref && ref.focus && ref.focus();\n    };\n    const watch = (name, defaultValue)=>isFunction(name) ? _subjects.values.subscribe({\n            next: (payload)=>name(_getWatch(undefined, defaultValue), payload)\n        }) : _getWatch(name, defaultValue, true);\n    const unregister = (name, options = {})=>{\n        for (const fieldName of name ? convertToArrayPayload(name) : _names.mount){\n            _names.mount.delete(fieldName);\n            _names.array.delete(fieldName);\n            if (!options.keepValue) {\n                unset(_fields, fieldName);\n                unset(_formValues, fieldName);\n            }\n            !options.keepError && unset(_formState.errors, fieldName);\n            !options.keepDirty && unset(_formState.dirtyFields, fieldName);\n            !options.keepTouched && unset(_formState.touchedFields, fieldName);\n            !options.keepIsValidating && unset(_formState.validatingFields, fieldName);\n            !_options.shouldUnregister && !options.keepDefaultValue && unset(_defaultValues, fieldName);\n        }\n        _subjects.values.next({\n            values: {\n                ..._formValues\n            }\n        });\n        _subjects.state.next({\n            ..._formState,\n            ...!options.keepDirty ? {} : {\n                isDirty: _getDirty()\n            }\n        });\n        !options.keepIsValid && _updateValid();\n    };\n    const _updateDisabledField = ({ disabled, name, field, fields })=>{\n        if (isBoolean(disabled) && _state.mount || !!disabled || _names.disabled.has(name)) {\n            disabled ? _names.disabled.add(name) : _names.disabled.delete(name);\n            updateTouchAndDirty(name, getFieldValue(field ? field._f : get(fields, name)._f), false, false, true);\n        }\n    };\n    const register = (name, options = {})=>{\n        let field = get(_fields, name);\n        const disabledIsDefined = isBoolean(options.disabled) || isBoolean(_options.disabled);\n        set(_fields, name, {\n            ...field || {},\n            _f: {\n                ...field && field._f ? field._f : {\n                    ref: {\n                        name\n                    }\n                },\n                name,\n                mount: true,\n                ...options\n            }\n        });\n        _names.mount.add(name);\n        if (field) {\n            _updateDisabledField({\n                field,\n                disabled: isBoolean(options.disabled) ? options.disabled : _options.disabled,\n                name\n            });\n        } else {\n            updateValidAndValue(name, true, options.value);\n        }\n        return {\n            ...disabledIsDefined ? {\n                disabled: options.disabled || _options.disabled\n            } : {},\n            ..._options.progressive ? {\n                required: !!options.required,\n                min: getRuleValue(options.min),\n                max: getRuleValue(options.max),\n                minLength: getRuleValue(options.minLength),\n                maxLength: getRuleValue(options.maxLength),\n                pattern: getRuleValue(options.pattern)\n            } : {},\n            name,\n            onChange,\n            onBlur: onChange,\n            ref: (ref)=>{\n                if (ref) {\n                    register(name, options);\n                    field = get(_fields, name);\n                    const fieldRef = isUndefined(ref.value) ? ref.querySelectorAll ? ref.querySelectorAll(\"input,select,textarea\")[0] || ref : ref : ref;\n                    const radioOrCheckbox = isRadioOrCheckbox(fieldRef);\n                    const refs = field._f.refs || [];\n                    if (radioOrCheckbox ? refs.find((option)=>option === fieldRef) : fieldRef === field._f.ref) {\n                        return;\n                    }\n                    set(_fields, name, {\n                        _f: {\n                            ...field._f,\n                            ...radioOrCheckbox ? {\n                                refs: [\n                                    ...refs.filter(live),\n                                    fieldRef,\n                                    ...Array.isArray(get(_defaultValues, name)) ? [\n                                        {}\n                                    ] : []\n                                ],\n                                ref: {\n                                    type: fieldRef.type,\n                                    name\n                                }\n                            } : {\n                                ref: fieldRef\n                            }\n                        }\n                    });\n                    updateValidAndValue(name, false, undefined, fieldRef);\n                } else {\n                    field = get(_fields, name, {});\n                    if (field._f) {\n                        field._f.mount = false;\n                    }\n                    (_options.shouldUnregister || options.shouldUnregister) && !(isNameInFieldArray(_names.array, name) && _state.action) && _names.unMount.add(name);\n                }\n            }\n        };\n    };\n    const _focusError = ()=>_options.shouldFocusError && iterateFieldsByAction(_fields, _focusInput, _names.mount);\n    const _disableForm = (disabled)=>{\n        if (isBoolean(disabled)) {\n            _subjects.state.next({\n                disabled\n            });\n            iterateFieldsByAction(_fields, (ref, name)=>{\n                const currentField = get(_fields, name);\n                if (currentField) {\n                    ref.disabled = currentField._f.disabled || disabled;\n                    if (Array.isArray(currentField._f.refs)) {\n                        currentField._f.refs.forEach((inputRef)=>{\n                            inputRef.disabled = currentField._f.disabled || disabled;\n                        });\n                    }\n                }\n            }, 0, false);\n        }\n    };\n    const handleSubmit = (onValid, onInvalid)=>async (e)=>{\n            let onValidError = undefined;\n            if (e) {\n                e.preventDefault && e.preventDefault();\n                e.persist && e.persist();\n            }\n            let fieldValues = cloneObject(_formValues);\n            if (_names.disabled.size) {\n                for (const name of _names.disabled){\n                    set(fieldValues, name, undefined);\n                }\n            }\n            _subjects.state.next({\n                isSubmitting: true\n            });\n            if (_options.resolver) {\n                const { errors, values } = await _executeSchema();\n                _formState.errors = errors;\n                fieldValues = values;\n            } else {\n                await executeBuiltInValidation(_fields);\n            }\n            unset(_formState.errors, \"root\");\n            if (isEmptyObject(_formState.errors)) {\n                _subjects.state.next({\n                    errors: {}\n                });\n                try {\n                    await onValid(fieldValues, e);\n                } catch (error) {\n                    onValidError = error;\n                }\n            } else {\n                if (onInvalid) {\n                    await onInvalid({\n                        ..._formState.errors\n                    }, e);\n                }\n                _focusError();\n                setTimeout(_focusError);\n            }\n            _subjects.state.next({\n                isSubmitted: true,\n                isSubmitting: false,\n                isSubmitSuccessful: isEmptyObject(_formState.errors) && !onValidError,\n                submitCount: _formState.submitCount + 1,\n                errors: _formState.errors\n            });\n            if (onValidError) {\n                throw onValidError;\n            }\n        };\n    const resetField = (name, options = {})=>{\n        if (get(_fields, name)) {\n            if (isUndefined(options.defaultValue)) {\n                setValue(name, cloneObject(get(_defaultValues, name)));\n            } else {\n                setValue(name, options.defaultValue);\n                set(_defaultValues, name, cloneObject(options.defaultValue));\n            }\n            if (!options.keepTouched) {\n                unset(_formState.touchedFields, name);\n            }\n            if (!options.keepDirty) {\n                unset(_formState.dirtyFields, name);\n                _formState.isDirty = options.defaultValue ? _getDirty(name, cloneObject(get(_defaultValues, name))) : _getDirty();\n            }\n            if (!options.keepError) {\n                unset(_formState.errors, name);\n                _proxyFormState.isValid && _updateValid();\n            }\n            _subjects.state.next({\n                ..._formState\n            });\n        }\n    };\n    const _reset = (formValues, keepStateOptions = {})=>{\n        const updatedValues = formValues ? cloneObject(formValues) : _defaultValues;\n        const cloneUpdatedValues = cloneObject(updatedValues);\n        const isEmptyResetValues = isEmptyObject(formValues);\n        const values = isEmptyResetValues ? _defaultValues : cloneUpdatedValues;\n        if (!keepStateOptions.keepDefaultValues) {\n            _defaultValues = updatedValues;\n        }\n        if (!keepStateOptions.keepValues) {\n            if (keepStateOptions.keepDirtyValues) {\n                const fieldsToCheck = new Set([\n                    ..._names.mount,\n                    ...Object.keys(getDirtyFields(_defaultValues, _formValues))\n                ]);\n                for (const fieldName of Array.from(fieldsToCheck)){\n                    get(_formState.dirtyFields, fieldName) ? set(values, fieldName, get(_formValues, fieldName)) : setValue(fieldName, get(values, fieldName));\n                }\n            } else {\n                if (isWeb && isUndefined(formValues)) {\n                    for (const name of _names.mount){\n                        const field = get(_fields, name);\n                        if (field && field._f) {\n                            const fieldReference = Array.isArray(field._f.refs) ? field._f.refs[0] : field._f.ref;\n                            if (isHTMLElement(fieldReference)) {\n                                const form = fieldReference.closest(\"form\");\n                                if (form) {\n                                    form.reset();\n                                    break;\n                                }\n                            }\n                        }\n                    }\n                }\n                _fields = {};\n            }\n            _formValues = _options.shouldUnregister ? keepStateOptions.keepDefaultValues ? cloneObject(_defaultValues) : {} : cloneObject(values);\n            _subjects.array.next({\n                values: {\n                    ...values\n                }\n            });\n            _subjects.values.next({\n                values: {\n                    ...values\n                }\n            });\n        }\n        _names = {\n            mount: keepStateOptions.keepDirtyValues ? _names.mount : new Set(),\n            unMount: new Set(),\n            array: new Set(),\n            disabled: new Set(),\n            watch: new Set(),\n            watchAll: false,\n            focus: \"\"\n        };\n        _state.mount = !_proxyFormState.isValid || !!keepStateOptions.keepIsValid || !!keepStateOptions.keepDirtyValues;\n        _state.watch = !!_options.shouldUnregister;\n        _subjects.state.next({\n            submitCount: keepStateOptions.keepSubmitCount ? _formState.submitCount : 0,\n            isDirty: isEmptyResetValues ? false : keepStateOptions.keepDirty ? _formState.isDirty : !!(keepStateOptions.keepDefaultValues && !deepEqual(formValues, _defaultValues)),\n            isSubmitted: keepStateOptions.keepIsSubmitted ? _formState.isSubmitted : false,\n            dirtyFields: isEmptyResetValues ? {} : keepStateOptions.keepDirtyValues ? keepStateOptions.keepDefaultValues && _formValues ? getDirtyFields(_defaultValues, _formValues) : _formState.dirtyFields : keepStateOptions.keepDefaultValues && formValues ? getDirtyFields(_defaultValues, formValues) : keepStateOptions.keepDirty ? _formState.dirtyFields : {},\n            touchedFields: keepStateOptions.keepTouched ? _formState.touchedFields : {},\n            errors: keepStateOptions.keepErrors ? _formState.errors : {},\n            isSubmitSuccessful: keepStateOptions.keepIsSubmitSuccessful ? _formState.isSubmitSuccessful : false,\n            isSubmitting: false\n        });\n    };\n    const reset = (formValues, keepStateOptions)=>_reset(isFunction(formValues) ? formValues(_formValues) : formValues, keepStateOptions);\n    const setFocus = (name, options = {})=>{\n        const field = get(_fields, name);\n        const fieldReference = field && field._f;\n        if (fieldReference) {\n            const fieldRef = fieldReference.refs ? fieldReference.refs[0] : fieldReference.ref;\n            if (fieldRef.focus) {\n                fieldRef.focus();\n                options.shouldSelect && isFunction(fieldRef.select) && fieldRef.select();\n            }\n        }\n    };\n    const _updateFormState = (updatedFormState)=>{\n        _formState = {\n            ..._formState,\n            ...updatedFormState\n        };\n    };\n    const _resetDefaultValues = ()=>isFunction(_options.defaultValues) && _options.defaultValues().then((values)=>{\n            reset(values, _options.resetOptions);\n            _subjects.state.next({\n                isLoading: false\n            });\n        });\n    return {\n        control: {\n            register,\n            unregister,\n            getFieldState,\n            handleSubmit,\n            setError,\n            _executeSchema,\n            _getWatch,\n            _getDirty,\n            _updateValid,\n            _removeUnmounted,\n            _updateFieldArray,\n            _updateDisabledField,\n            _getFieldArray,\n            _reset,\n            _resetDefaultValues,\n            _updateFormState,\n            _disableForm,\n            _subjects,\n            _proxyFormState,\n            _setErrors,\n            get _fields () {\n                return _fields;\n            },\n            get _formValues () {\n                return _formValues;\n            },\n            get _state () {\n                return _state;\n            },\n            set _state (value){\n                _state = value;\n            },\n            get _defaultValues () {\n                return _defaultValues;\n            },\n            get _names () {\n                return _names;\n            },\n            set _names (value){\n                _names = value;\n            },\n            get _formState () {\n                return _formState;\n            },\n            set _formState (value){\n                _formState = value;\n            },\n            get _options () {\n                return _options;\n            },\n            set _options (value){\n                _options = {\n                    ..._options,\n                    ...value\n                };\n            }\n        },\n        trigger,\n        register,\n        handleSubmit,\n        watch,\n        setValue,\n        getValues,\n        reset,\n        resetField,\n        clearErrors,\n        unregister,\n        setError,\n        setFocus,\n        getFieldState\n    };\n}\n/**\n * Custom hook to manage the entire form.\n *\n * @remarks\n * [API](https://react-hook-form.com/docs/useform) • [Demo](https://codesandbox.io/s/react-hook-form-get-started-ts-5ksmm) • [Video](https://www.youtube.com/watch?v=RkXv4AXXC_4)\n *\n * @param props - form configuration and validation parameters.\n *\n * @returns methods - individual functions to manage the form state. {@link UseFormReturn}\n *\n * @example\n * ```tsx\n * function App() {\n *   const { register, handleSubmit, watch, formState: { errors } } = useForm();\n *   const onSubmit = data => console.log(data);\n *\n *   console.log(watch(\"example\"));\n *\n *   return (\n *     <form onSubmit={handleSubmit(onSubmit)}>\n *       <input defaultValue=\"test\" {...register(\"example\")} />\n *       <input {...register(\"exampleRequired\", { required: true })} />\n *       {errors.exampleRequired && <span>This field is required</span>}\n *       <button>Submit</button>\n *     </form>\n *   );\n * }\n * ```\n */ function useForm(props = {}) {\n    const _formControl = react__WEBPACK_IMPORTED_MODULE_0__.useRef(undefined);\n    const _values = react__WEBPACK_IMPORTED_MODULE_0__.useRef(undefined);\n    const [formState, updateFormState] = react__WEBPACK_IMPORTED_MODULE_0__.useState({\n        isDirty: false,\n        isValidating: false,\n        isLoading: isFunction(props.defaultValues),\n        isSubmitted: false,\n        isSubmitting: false,\n        isSubmitSuccessful: false,\n        isValid: false,\n        submitCount: 0,\n        dirtyFields: {},\n        touchedFields: {},\n        validatingFields: {},\n        errors: props.errors || {},\n        disabled: props.disabled || false,\n        defaultValues: isFunction(props.defaultValues) ? undefined : props.defaultValues\n    });\n    if (!_formControl.current) {\n        _formControl.current = {\n            ...createFormControl(props),\n            formState\n        };\n    }\n    const control = _formControl.current.control;\n    control._options = props;\n    useSubscribe({\n        subject: control._subjects.state,\n        next: (value1)=>{\n            if (shouldRenderFormState(value1, control._proxyFormState, control._updateFormState, true)) {\n                updateFormState({\n                    ...control._formState\n                });\n            }\n        }\n    });\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>control._disableForm(props.disabled), [\n        control,\n        props.disabled\n    ]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        if (control._proxyFormState.isDirty) {\n            const isDirty = control._getDirty();\n            if (isDirty !== formState.isDirty) {\n                control._subjects.state.next({\n                    isDirty\n                });\n            }\n        }\n    }, [\n        control,\n        formState.isDirty\n    ]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        if (props.values && !deepEqual(props.values, _values.current)) {\n            control._reset(props.values, control._options.resetOptions);\n            _values.current = props.values;\n            updateFormState((state)=>({\n                    ...state\n                }));\n        } else {\n            control._resetDefaultValues();\n        }\n    }, [\n        props.values,\n        control\n    ]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        if (props.errors) {\n            control._setErrors(props.errors);\n        }\n    }, [\n        props.errors,\n        control\n    ]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        if (!control._state.mount) {\n            control._updateValid();\n            control._state.mount = true;\n        }\n        if (control._state.watch) {\n            control._state.watch = false;\n            control._subjects.state.next({\n                ...control._formState\n            });\n        }\n        control._removeUnmounted();\n    });\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        props.shouldUnregister && control._subjects.values.next({\n            values: control._getWatch()\n        });\n    }, [\n        props.shouldUnregister,\n        control\n    ]);\n    _formControl.current.formState = getProxyFormState(formState, control);\n    return _formControl.current;\n}\n //# sourceMappingURL=index.esm.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-hook-form/dist/index.esm.mjs\n");

/***/ })

};
;